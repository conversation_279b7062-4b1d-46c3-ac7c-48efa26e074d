.progress-indicator {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 2rem;
  border-radius: 12px;
  margin: 1rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.progress-header h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.progress-header p {
  margin: 0 0 2rem 0;
  color: #666;
  font-size: 0.9rem;
}

.progress-stages {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  position: relative;
}

.progress-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  min-width: 80px;
}

.stage-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  border: 3px solid #ddd;
  background: white;
}

.progress-stage.pending .stage-icon {
  background: #f8f9fa;
  color: #999;
  border-color: #ddd;
}

.progress-stage.active .stage-icon {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
  animation: pulse 1.5s infinite;
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.progress-stage.completed .stage-icon {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
  transform: scale(1.1);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(76, 175, 80, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
  }
}

.stage-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
  text-align: center;
  transition: color 0.3s ease;
}

.progress-stage.active .stage-label {
  color: #4CAF50;
}

.progress-stage.completed .stage-label {
  color: #4CAF50;
}

.stage-connector {
  position: absolute;
  top: 25px;
  left: 65px;
  width: 40px;
  height: 3px;
  background: #ddd;
  transition: background 0.3s ease;
}

.stage-connector.completed {
  background: #4CAF50;
}

.progress-bar-container {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
  border-radius: 3px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-tips {
  background: rgba(76, 175, 80, 0.1);
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.progress-tips p {
  margin: 0;
  color: #2e7d32;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .progress-indicator {
    padding: 1.5rem;
  }
  
  .progress-stages {
    gap: 0.5rem;
  }
  
  .progress-stage {
    min-width: 60px;
  }
  
  .stage-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .stage-connector {
    top: 20px;
    left: 50px;
    width: 30px;
  }
  
  .stage-label {
    font-size: 0.7rem;
  }
  
  .progress-header h3 {
    font-size: 1.1rem;
  }
}
