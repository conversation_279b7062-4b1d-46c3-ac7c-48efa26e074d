.dashboard-charts {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin: 2rem 0;
}

/* Environmental Impact Summary */
.impact-summary {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
}

.impact-summary h3 {
  margin: 0 0 1.5rem 0;
  text-align: center;
  font-size: 1.4rem;
  font-weight: 600;
}

.impact-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.impact-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.3s ease;
}

.impact-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.2);
}

.impact-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.impact-value {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.impact-label {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 500;
}

/* Chart Sections */
.chart-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.chart-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Waste Type Chart */
.waste-type-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.waste-type-bar {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  transition: transform 0.2s ease;
}

.waste-type-bar:hover {
  transform: translateX(5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bar-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.waste-icon {
  font-size: 1.2rem;
  width: 30px;
  text-align: center;
}

.waste-type {
  flex: 1;
  font-weight: 600;
  color: #2c3e50;
}

.percentage {
  font-weight: bold;
  color: #4CAF50;
  font-size: 1.1rem;
}

.bar-container {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s ease;
  position: relative;
}

.bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.bar-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: #666;
}

/* Weekly Chart */
.weekly-chart {
  display: flex;
  align-items: end;
  gap: 1rem;
  height: 200px;
  padding: 1rem 0;
  border-bottom: 2px solid #e9ecef;
  position: relative;
}

.day-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.day-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
  margin-bottom: auto;
}

.day-bar-container {
  height: 120px;
  width: 100%;
  display: flex;
  align-items: end;
  justify-content: center;
  position: relative;
}

.day-bar {
  width: 60%;
  background: linear-gradient(180deg, #4CAF50 0%, #45a049 100%);
  border-radius: 4px 4px 0 0;
  transition: height 0.8s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.day-bar:hover {
  background: linear-gradient(180deg, #66BB6A 0%, #4CAF50 100%);
  transform: scaleX(1.1);
}

.day-value {
  font-weight: bold;
  color: #4CAF50;
  font-size: 0.9rem;
}

.day-count {
  font-size: 0.7rem;
  color: #999;
}

.no-data-message {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-style: italic;
}

.no-data-message p {
  margin: 0;
  font-size: 1rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .dashboard-charts {
    gap: 1.5rem;
    margin: 1rem 0;
  }
  
  .impact-summary,
  .chart-section {
    padding: 1.5rem;
  }
  
  .impact-cards {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .impact-card {
    padding: 1rem;
  }
  
  .impact-value {
    font-size: 1.5rem;
  }
  
  .bar-header {
    gap: 0.5rem;
  }
  
  .waste-type {
    font-size: 0.9rem;
  }
  
  .bar-details {
    font-size: 0.8rem;
    gap: 0.5rem;
  }
  
  .weekly-chart {
    height: 150px;
    gap: 0.5rem;
  }
  
  .day-bar-container {
    height: 80px;
  }
  
  .day-label,
  .day-value,
  .day-count {
    font-size: 0.7rem;
  }
}
