// UploadWaste.jsx
import { useState, useRef } from 'react';
import { usePoints } from './PointsContext';
import WasteTypeGuide from './components/WasteTypeGuide';
import AnimatedPointsDisplay from './components/AnimatedPointsDisplay';
import ProgressIndicator from './components/ProgressIndicator';
import './waste.css';

const wasteTypes = [
  {
    type: 'Plastic',
    points: 100,
    description: 'Plastic bottles, containers, packaging',
    minWeight: 0.1,
    maxWeight: 5,
    examples: ['Water bottles', 'Food containers', 'Plastic bags', 'Bottle caps'],
    tips: 'Clean containers before recycling. Remove labels when possible.',
    icon: '♻️'
  },
  {
    type: 'Glass',
    points: 150,
    description: 'Glass bottles, jars, containers',
    minWeight: 0.2,
    maxWeight: 3,
    examples: ['Wine bottles', 'Jam jars', 'Glass containers', 'Beer bottles'],
    tips: 'Remove metal lids. Rinse clean. Separate by color when possible.',
    icon: '🍾'
  },
  {
    type: 'Metal',
    points: 120,
    description: 'Metal cans, aluminum, steel containers',
    minWeight: 0.1,
    maxWeight: 2,
    examples: ['Aluminum cans', 'Steel cans', 'Metal lids', 'Foil containers'],
    tips: 'Rinse clean. Crush cans to save space. Remove paper labels.',
    icon: '🥫'
  },
  {
    type: 'Paper',
    points: 80,
    description: 'Paper, cardboard, newspapers',
    minWeight: 0.05,
    maxWeight: 2,
    examples: ['Newspapers', 'Cardboard boxes', 'Office paper', 'Magazines'],
    tips: 'Keep dry. Remove plastic tape and staples. Flatten boxes.',
    icon: '📄'
  },
  {
    type: 'Organic',
    points: 60,
    description: 'Food waste, organic materials',
    minWeight: 0.1,
    maxWeight: 3,
    examples: ['Fruit peels', 'Vegetable scraps', 'Coffee grounds', 'Eggshells'],
    tips: 'Compost when possible. Avoid meat and dairy in home composting.',
    icon: '🍃'
  }
];

export default function UploadWaste() {
  const { addSubmission } = usePoints();
  const [image, setImage] = useState(null);
  const [location, setLocation] = useState('');
  const [imagePreview, setImagePreview] = useState(null);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [analysisStage, setAnalysisStage] = useState('idle'); // idle, uploading, analyzing, processing
  const [showPointsAnimation, setShowPointsAnimation] = useState(false);
  const [earnedPoints, setEarnedPoints] = useState(0);
  const fileInputRef = useRef(null);



  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      processImageFile(file);
    }
  };

  const processImageFile = (file) => {
    setImage(file);
    setAnalysisStage('uploading');
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreview(reader.result);
      setAnalysisStage('analyzing');
      // Start analysis when image is loaded - pass the file directly
      analyzeWasteImageWithFile(file);
    };
    reader.readAsDataURL(file);
  };

  // Drag and drop handlers
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      processImageFile(e.dataTransfer.files[0]);
    }
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  // New function that takes the file directly
  const analyzeWasteImageWithFile = async (file) => {
    setIsAnalyzing(true);
    setAnalysisStage('analyzing');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('http://localhost:3003/api/analyze-waste', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      setAnalysisStage('processing');
      const result = await response.json();
      console.log('📊 Analysis result received:', result);

      // Calculate total points for animation
      let totalPoints = 0;
      if (result.wasteTypes && result.wasteTypes.length > 0) {
        totalPoints = result.wasteTypes.reduce((sum, item) => sum + (item.points || 0), 0);
      }

      setAnalysisResult(result);
      setEarnedPoints(totalPoints);

      // Trigger points animation if points were earned
      if (totalPoints > 0) {
        setShowPointsAnimation(true);
        setTimeout(() => setShowPointsAnimation(false), 3000);
      }

    } catch (error) {
      console.error('Analysis failed:', error);
      setAnalysisStage('processing');
      // Fallback to mock analysis if API fails
      setTimeout(() => {
        const randomTypeIndex = Math.floor(Math.random() * wasteTypes.length);
        const selectedType = wasteTypes[randomTypeIndex];
        const randomWeight = (
          Math.random() * (selectedType.maxWeight - selectedType.minWeight) +
          selectedType.minWeight
        ).toFixed(1);

        const mockResult = {
          wasteTypes: [{
            type: selectedType.type,
            weight: parseFloat(randomWeight),
            quantity: 1,
            points: selectedType.points,
            confidence: 75.0
          }],
          totalPoints: selectedType.points,
          totalWeight: parseFloat(randomWeight),
          fallback: true,
          detectedLabels: ['fallback analysis']
        };

        setAnalysisResult(mockResult);
        setEarnedPoints(selectedType.points);
        setShowPointsAnimation(true);
        setTimeout(() => setShowPointsAnimation(false), 3000);

        setIsAnalyzing(false);
        setAnalysisStage('idle');
      }, 2000);
      return;
    } finally {
      setIsAnalyzing(false);
      setAnalysisStage('idle');
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!image || !location || !analysisResult) {
      alert('Please fill in all required fields and wait for analysis to complete');
      return;
    }

    // Handle waste types from Gemini API response
    if (analysisResult.wasteTypes && analysisResult.wasteTypes.length > 0) {
      // Create separate submissions for each waste type
      analysisResult.wasteTypes.forEach((wasteType, index) => {
        const newSubmission = {
          id: Date.now() + index, // Ensure unique IDs
          date: new Date().toISOString().slice(0, 10),
          type: wasteType.specificType || wasteType.type,
          points: wasteType.points || 0,
          location,
          imageUrl: imagePreview,
          weight: wasteType.weight || 0,
          quantity: wasteType.quantity || 0,
          confidence: wasteType.confidence || 0,
          multipleTypesSubmission: analysisResult.wasteTypes.length > 1,
          originalAnalysisId: Date.now(), // Link related submissions
          apiSource: analysisResult.apiSource
        };
        addSubmission(newSubmission);
      });
    } else {
      // Handle error case or no waste detected
      alert('No waste detected in the image. Please try with a different image.');
      return;
    }

    // Reset form
    setImage(null);
    setImagePreview(null);
    setLocation('');
    setAnalysisResult(null);
    e.target.reset();

    // Show success message
    alert(analysisResult.wasteTypes.length > 1
      ? `Successfully submitted ${analysisResult.wasteTypes.length} waste types!`
      : 'Waste submission successful!'
    );
  };

  return (
    <div className="upload-container">
      <h2>Upload Your Waste</h2>

      {/* Waste Type Guide */}
      <WasteTypeGuide wasteTypes={wasteTypes} />

      {/* Points Animation */}
      {showPointsAnimation && (
        <AnimatedPointsDisplay points={earnedPoints} />
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>Waste Image</label>

          {/* Enhanced Drag and Drop Zone */}
          <div
            className={`drag-drop-zone ${dragActive ? 'drag-active' : ''} ${imagePreview ? 'has-image' : ''}`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={handleClick}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              style={{ display: 'none' }}
              required
            />

            {!imagePreview ? (
              <div className="drop-zone-content">
                <div className="upload-icon">📸</div>
                <p className="drop-text">
                  <strong>Click to upload</strong> or drag and drop your waste image here
                </p>
                <p className="file-types">PNG, JPG, GIF up to 10MB</p>
              </div>
            ) : (
              <div className="image-preview">
                <img src={imagePreview} alt="Waste preview" />
                <div className="image-overlay">
                  <button type="button" className="change-image-btn" onClick={handleClick}>
                    Change Image
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {isAnalyzing ? (
          <ProgressIndicator stage={analysisStage} />
        ) : analysisResult && (
          <div className="analysis-result">
            <h3>Analysis Results</h3>

            {analysisResult.wasteTypes && analysisResult.wasteTypes.length > 1 ? (
              <div className="multiple-types-result">
                <div className="summary-section">
                  <h4>Summary</h4>
                  <div className="result-item">
                    <span>Total Types Detected:</span> {analysisResult.wasteTypes.length}
                  </div>
                  <div className="result-item">
                    <span>Total Weight:</span> {analysisResult.totalWeight} kg
                  </div>
                  <div className="result-item">
                    <span>Total Quantity:</span> {analysisResult.wasteTypes.reduce((sum, item) => sum + item.quantity, 0)} items
                  </div>
                  <div className="result-item points">
                    <span>Total Points Earned:</span> {analysisResult.totalPoints}
                  </div>
                </div>

                <div className="detailed-breakdown">
                  <h4>Breakdown by Type</h4>
                  {analysisResult.wasteTypes.map((wasteType, index) => (
                    <div key={index} className="waste-type-breakdown">
                      <h5>{wasteType.specificType || wasteType.type}</h5>
                      {wasteType.specificType && wasteType.specificType !== wasteType.type && (
                        <div className="result-item specific-type">
                          <span>Category:</span> {wasteType.type}
                        </div>
                      )}
                      <div className="breakdown-details">
                        <div className="result-item">
                          <span>Weight:</span> {wasteType.weight} kg
                        </div>
                        <div className="result-item">
                          <span>Quantity:</span> {wasteType.quantity} items
                        </div>
                        <div className="result-item">
                          <span>Points:</span> {wasteType.points}
                        </div>
                        <div className="result-item">
                          <span>Confidence:</span> {wasteType.confidence}%
                        </div>
                        {wasteType.estimatedSize && (
                          <div className="result-item">
                            <span>Size:</span> {wasteType.estimatedSize}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : analysisResult.wasteTypes && analysisResult.wasteTypes.length === 1 ? (
              <div className="single-type-result">
                <div className="result-item">
                  <span>Type:</span> {analysisResult.wasteTypes[0].specificType || analysisResult.wasteTypes[0].type}
                </div>
                {analysisResult.wasteTypes[0].specificType && analysisResult.wasteTypes[0].specificType !== analysisResult.wasteTypes[0].type && (
                  <div className="result-item">
                    <span>Category:</span> {analysisResult.wasteTypes[0].type}
                  </div>
                )}
                <div className="result-item">
                  <span>Weight:</span> {analysisResult.wasteTypes[0].weight} kg
                </div>
                <div className="result-item">
                  <span>Quantity:</span> {analysisResult.wasteTypes[0].quantity} items
                </div>
                <div className="result-item">
                  <span>Confidence:</span> {analysisResult.wasteTypes[0].confidence * 100}%
                </div>
                <div className="result-item points">
                  <span>Points Earned:</span> {analysisResult.wasteTypes[0].points}
                </div>
                {analysisResult.apiSource && (
                  <div className="result-item api-source">
                    <span>Analysis Source:</span> {analysisResult.apiSource}
                  </div>
                )}
              </div>
            ) : (
              <div className="error-result">
                <div className="result-item">
                  <span>Error:</span> {analysisResult.error || 'No waste detected'}
                </div>
                {analysisResult.note && (
                  <div className="result-item">
                    <span>Note:</span> {analysisResult.note}
                  </div>
                )}
                {analysisResult.apiSource && (
                  <div className="result-item api-source">
                    <span>Analysis Source:</span> {analysisResult.apiSource}
                  </div>
                )}
              </div>
            )}

            {analysisResult.detectedLabels && analysisResult.detectedLabels.length > 0 && (
              <div className="detected-labels">
                <h4>Detected Labels</h4>
                <div className="labels-list">
                  {analysisResult.detectedLabels.slice(0, 5).map((label, index) => (
                    <span key={index} className="label-tag">{label}</span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="form-group">
          <label>Location</label>
          <input 
            type="text" 
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="Enter waste location"
            required
          />
        </div>

        <button 
          type="submit" 
          className="submit-btn"
          disabled={isAnalyzing || !analysisResult}
        >
          {isAnalyzing ? 'Analyzing...' : 'Submit Waste'}
        </button>
      </form>
    </div>
  );
}
