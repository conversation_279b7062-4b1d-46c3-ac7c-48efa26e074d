import './ProgressIndicator.css';

export default function ProgressIndicator({ stage }) {
  const stages = [
    { key: 'uploading', label: 'Uploading', icon: '📤' },
    { key: 'analyzing', label: 'Analyzing', icon: '🔍' },
    { key: 'processing', label: 'Processing Results', icon: '⚙️' }
  ];

  const getCurrentStageIndex = () => {
    return stages.findIndex(s => s.key === stage);
  };

  const currentIndex = getCurrentStageIndex();

  return (
    <div className="progress-indicator">
      <div className="progress-header">
        <h3>Analyzing Your Waste</h3>
        <p>Please wait while we process your image...</p>
      </div>
      
      <div className="progress-stages">
        {stages.map((stageItem, index) => (
          <div 
            key={stageItem.key}
            className={`progress-stage ${
              index < currentIndex ? 'completed' : 
              index === currentIndex ? 'active' : 'pending'
            }`}
          >
            <div className="stage-icon">
              {index < currentIndex ? '✅' : stageItem.icon}
            </div>
            <div className="stage-label">{stageItem.label}</div>
            {index < stages.length - 1 && (
              <div className={`stage-connector ${index < currentIndex ? 'completed' : ''}`}></div>
            )}
          </div>
        ))}
      </div>
      
      <div className="progress-bar-container">
        <div 
          className="progress-bar"
          style={{ width: `${((currentIndex + 1) / stages.length) * 100}%` }}
        ></div>
      </div>
      
      <div className="progress-tips">
        {stage === 'uploading' && (
          <p>📤 Uploading your image securely...</p>
        )}
        {stage === 'analyzing' && (
          <p>🤖 Our AI is identifying waste types and calculating points...</p>
        )}
        {stage === 'processing' && (
          <p>⚡ Almost done! Finalizing your results...</p>
        )}
      </div>
    </div>
  );
}
