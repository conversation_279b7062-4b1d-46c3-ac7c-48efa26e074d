.waste-type-guide {
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
}

.waste-type-guide h3 {
  margin: 0 0 1rem 0;
  color: #2e7d32;
  text-align: center;
  font-size: 1.2rem;
}

.guide-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.guide-card {
  background: white;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.guide-card:hover {
  border-color: #4CAF50;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.15);
  transform: translateY(-2px);
}

.guide-card.expanded {
  border-color: #4CAF50;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  gap: 0.75rem;
}

.waste-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.card-title {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-title h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1rem;
}

.points-badge {
  background: #4CAF50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.expand-icon {
  font-size: 1.2rem;
  color: #666;
  font-weight: bold;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 50%;
}

.card-content {
  padding: 0 1rem 1rem 1rem;
  border-top: 1px solid #f0f0f0;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 300px;
  }
}

.description {
  margin: 1rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.examples-section,
.tips-section {
  margin: 1rem 0;
}

.examples-section h5,
.tips-section h5 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 0.9rem;
}

.examples-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.example-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.tips {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
  font-style: italic;
}

.weight-range {
  margin-top: 1rem;
  padding-top: 0.5rem;
  border-top: 1px solid #f0f0f0;
}

.weight-range small {
  color: #999;
  font-size: 0.8rem;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .guide-cards {
    grid-template-columns: 1fr;
  }
  
  .waste-type-guide {
    padding: 1rem;
  }
  
  .card-header {
    padding: 0.75rem;
  }
  
  .card-content {
    padding: 0 0.75rem 0.75rem 0.75rem;
  }
}
