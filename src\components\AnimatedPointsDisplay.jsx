import { useState, useEffect } from 'react';
import './AnimatedPointsDisplay.css';

export default function AnimatedPointsDisplay({ points }) {
  const [displayPoints, setDisplayPoints] = useState(0);
  const [showCelebration, setShowCelebration] = useState(false);

  useEffect(() => {
    // Animate points counting up
    const duration = 1500; // 1.5 seconds
    const steps = 30;
    const increment = points / steps;
    let currentStep = 0;

    const timer = setInterval(() => {
      currentStep++;
      if (currentStep <= steps) {
        setDisplayPoints(Math.floor(increment * currentStep));
      } else {
        setDisplayPoints(points);
        clearInterval(timer);
        // Show celebration after counting is done
        setShowCelebration(true);
        setTimeout(() => setShowCelebration(false), 1000);
      }
    }, duration / steps);

    return () => clearInterval(timer);
  }, [points]);

  return (
    <div className="animated-points-display">
      <div className={`points-container ${showCelebration ? 'celebrating' : ''}`}>
        <div className="points-icon">🎉</div>
        <div className="points-content">
          <h3>Points Earned!</h3>
          <div className="points-value">
            +{displayPoints}
          </div>
          <p>Great job recycling!</p>
        </div>
        <div className="points-icon">♻️</div>
      </div>
      
      {showCelebration && (
        <div className="celebration-effects">
          <div className="confetti">
            {[...Array(12)].map((_, i) => (
              <div key={i} className={`confetti-piece confetti-${i + 1}`}></div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
