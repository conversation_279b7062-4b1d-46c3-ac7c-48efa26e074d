.animated-points-display {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  animation: slideInScale 0.5s ease-out;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.points-container {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 300px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.points-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.points-container.celebrating {
  animation: celebrate 0.6s ease-in-out;
}

@keyframes celebrate {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.05); }
  50% { transform: scale(1.1); }
  75% { transform: scale(1.05); }
}

.points-icon {
  font-size: 2rem;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.points-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.points-value {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0.5rem 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  animation: countUp 1.5s ease-out;
}

@keyframes countUp {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.points-content p {
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.celebration-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.confetti {
  position: absolute;
  width: 100%;
  height: 100%;
}

.confetti-piece {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #FFD700;
  animation: confettiFall 1s ease-out forwards;
}

.confetti-piece:nth-child(2n) { background: #FF6B6B; }
.confetti-piece:nth-child(3n) { background: #4ECDC4; }
.confetti-piece:nth-child(4n) { background: #45B7D1; }
.confetti-piece:nth-child(5n) { background: #96CEB4; }

.confetti-1 { top: 10%; left: 10%; animation-delay: 0s; }
.confetti-2 { top: 20%; left: 20%; animation-delay: 0.1s; }
.confetti-3 { top: 10%; left: 30%; animation-delay: 0.2s; }
.confetti-4 { top: 30%; left: 40%; animation-delay: 0.3s; }
.confetti-5 { top: 10%; left: 50%; animation-delay: 0.4s; }
.confetti-6 { top: 20%; left: 60%; animation-delay: 0.5s; }
.confetti-7 { top: 10%; left: 70%; animation-delay: 0.6s; }
.confetti-8 { top: 30%; left: 80%; animation-delay: 0.7s; }
.confetti-9 { top: 10%; left: 90%; animation-delay: 0.8s; }
.confetti-10 { top: 40%; left: 15%; animation-delay: 0.9s; }
.confetti-11 { top: 40%; left: 85%; animation-delay: 1s; }
.confetti-12 { top: 50%; left: 50%; animation-delay: 1.1s; }

@keyframes confettiFall {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(200px) rotate(720deg);
    opacity: 0;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .points-container {
    min-width: 280px;
    padding: 1.5rem;
    margin: 0 1rem;
  }
  
  .points-value {
    font-size: 2rem;
  }
  
  .points-icon {
    font-size: 1.5rem;
  }
}
