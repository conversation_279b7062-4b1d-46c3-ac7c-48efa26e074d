import { useMemo } from 'react';
import './DashboardCharts.css';

export default function DashboardCharts({ submissions }) {
  const chartData = useMemo(() => {
    if (!submissions || submissions.length === 0) {
      return {
        wasteTypeData: [],
        weeklyData: [],
        totalImpact: { weight: 0, points: 0, co2Saved: 0 }
      };
    }

    // Calculate waste type distribution
    const wasteTypeCounts = {};
    const wasteTypeWeights = {};
    const wasteTypePoints = {};
    
    submissions.forEach(sub => {
      const type = sub.type || 'Unknown';
      wasteTypeCounts[type] = (wasteTypeCounts[type] || 0) + 1;
      wasteTypeWeights[type] = (wasteTypeWeights[type] || 0) + (Number(sub.weight) || 0);
      wasteTypePoints[type] = (wasteTypePoints[type] || 0) + (Number(sub.points) || 0);
    });

    const wasteTypeData = Object.keys(wasteTypeCounts).map(type => ({
      type,
      count: wasteTypeCounts[type],
      weight: wasteTypeWeights[type],
      points: wasteTypePoints[type],
      percentage: (wasteTypeCounts[type] / submissions.length * 100).toFixed(1)
    }));

    // Calculate weekly progress (last 7 days)
    const now = new Date();
    const weeklyData = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const daySubmissions = submissions.filter(sub => sub.date === dateStr);
      const dayPoints = daySubmissions.reduce((sum, sub) => sum + (Number(sub.points) || 0), 0);
      const dayWeight = daySubmissions.reduce((sum, sub) => sum + (Number(sub.weight) || 0), 0);
      
      weeklyData.push({
        date: dateStr,
        day: date.toLocaleDateString('en', { weekday: 'short' }),
        points: dayPoints,
        weight: dayWeight,
        count: daySubmissions.length
      });
    }

    // Calculate total environmental impact
    const totalWeight = submissions.reduce((sum, sub) => sum + (Number(sub.weight) || 0), 0);
    const totalPoints = submissions.reduce((sum, sub) => sum + (Number(sub.points) || 0), 0);
    const co2Saved = totalWeight * 2.3; // Approximate CO2 savings per kg of recycled waste

    return {
      wasteTypeData,
      weeklyData,
      totalImpact: { weight: totalWeight, points: totalPoints, co2Saved }
    };
  }, [submissions]);

  const getWasteTypeIcon = (type) => {
    const icons = {
      'Plastic': '♻️',
      'Glass': '🍾',
      'Metal': '🥫',
      'Paper': '📄',
      'Organic': '🍃',
      'Electronic': '📱',
      'Hazardous': '⚠️'
    };
    return icons[type] || '📦';
  };

  const getWasteTypeColor = (type) => {
    const colors = {
      'Plastic': '#2196F3',
      'Glass': '#4CAF50',
      'Metal': '#FF9800',
      'Paper': '#795548',
      'Organic': '#8BC34A',
      'Electronic': '#9C27B0',
      'Hazardous': '#F44336'
    };
    return colors[type] || '#607D8B';
  };

  const maxWeeklyPoints = Math.max(...chartData.weeklyData.map(d => d.points), 1);

  return (
    <div className="dashboard-charts">
      {/* Environmental Impact Summary */}
      <div className="impact-summary">
        <h3>🌍 Your Environmental Impact</h3>
        <div className="impact-cards">
          <div className="impact-card">
            <div className="impact-icon">♻️</div>
            <div className="impact-value">{chartData.totalImpact.weight.toFixed(1)} kg</div>
            <div className="impact-label">Waste Recycled</div>
          </div>
          <div className="impact-card">
            <div className="impact-icon">🎯</div>
            <div className="impact-value">{chartData.totalImpact.points}</div>
            <div className="impact-label">Points Earned</div>
          </div>
          <div className="impact-card">
            <div className="impact-icon">🌱</div>
            <div className="impact-value">{chartData.totalImpact.co2Saved.toFixed(1)} kg</div>
            <div className="impact-label">CO₂ Saved</div>
          </div>
        </div>
      </div>

      {/* Waste Type Distribution */}
      <div className="chart-section">
        <h3>📊 Waste Type Distribution</h3>
        <div className="waste-type-chart">
          {chartData.wasteTypeData.map((item, index) => (
            <div key={item.type} className="waste-type-bar">
              <div className="bar-header">
                <span className="waste-icon">{getWasteTypeIcon(item.type)}</span>
                <span className="waste-type">{item.type}</span>
                <span className="percentage">{item.percentage}%</span>
              </div>
              <div className="bar-container">
                <div 
                  className="bar-fill"
                  style={{ 
                    width: `${item.percentage}%`,
                    backgroundColor: getWasteTypeColor(item.type)
                  }}
                ></div>
              </div>
              <div className="bar-details">
                <span>{item.count} items</span>
                <span>{item.weight.toFixed(1)} kg</span>
                <span>{item.points} pts</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Weekly Progress */}
      <div className="chart-section">
        <h3>📈 Weekly Progress</h3>
        <div className="weekly-chart">
          {chartData.weeklyData.map((day, index) => (
            <div key={day.date} className="day-column">
              <div className="day-label">{day.day}</div>
              <div className="day-bar-container">
                <div
                  className="day-bar"
                  style={{
                    height: `${(day.points / maxWeeklyPoints) * 100}%`,
                    minHeight: day.points > 0 ? '10px' : '2px'
                  }}
                ></div>
              </div>
              <div className="day-value">{day.points}</div>
              <div className="day-count">{day.count} items</div>
            </div>
          ))}
        </div>
        {chartData.weeklyData.every(day => day.points === 0) && (
          <div className="no-data-message">
            <p>📊 Start recycling to see your weekly progress!</p>
          </div>
        )}
      </div>
    </div>
  );
}
