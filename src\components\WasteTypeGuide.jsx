import { useState } from 'react';
import './WasteTypeGuide.css';

export default function WasteTypeGuide({ wasteTypes }) {
  const [expandedCard, setExpandedCard] = useState(null);

  const toggleCard = (index) => {
    setExpandedCard(expandedCard === index ? null : index);
  };

  return (
    <div className="waste-type-guide">
      <h3>💡 Waste Type Guide - Maximize Your Points!</h3>
      <div className="guide-cards">
        {wasteTypes.map((waste, index) => (
          <div 
            key={waste.type} 
            className={`guide-card ${expandedCard === index ? 'expanded' : ''}`}
            onClick={() => toggleCard(index)}
          >
            <div className="card-header">
              <span className="waste-icon">{waste.icon}</span>
              <div className="card-title">
                <h4>{waste.type}</h4>
                <span className="points-badge">{waste.points} pts</span>
              </div>
              <span className="expand-icon">{expandedCard === index ? '−' : '+'}</span>
            </div>
            
            {expandedCard === index && (
              <div className="card-content">
                <p className="description">{waste.description}</p>
                
                <div className="examples-section">
                  <h5>Examples:</h5>
                  <div className="examples-list">
                    {waste.examples.map((example, idx) => (
                      <span key={idx} className="example-tag">{example}</span>
                    ))}
                  </div>
                </div>
                
                <div className="tips-section">
                  <h5>💡 Recycling Tips:</h5>
                  <p className="tips">{waste.tips}</p>
                </div>
                
                <div className="weight-range">
                  <small>Weight range: {waste.minWeight}kg - {waste.maxWeight}kg</small>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
